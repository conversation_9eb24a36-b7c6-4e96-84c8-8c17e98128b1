import React from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
// 1. Import Animated and Keyframe
import Animated, { Keyframe } from 'react-native-reanimated';
// 2. Import chosen icon set (e.g., Feather)
import { Feather } from '@expo/vector-icons'; // Or MaterialCommunityIcons, etc.
import { useColorScheme } from 'nativewind';

interface ActionMenuProps {
  onClose: () => void;
  onAddProfile: () => void;
  onAddNote: () => void;
  onAddDate: () => void;
  onAddGift: () => void;
  onEditProfile: () => void;
  className?: string;
}

const AnimatedTouchableOpacity =
  Animated.createAnimatedComponent(TouchableOpacity);

// 3. Define menu items with labels AND icons
const menuItems = [
  {
    label: 'Add Note',
    iconName: 'file-plus', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddNote();
      props.onClose();
    },
  },
  {
    label: 'Add Important Date',
    iconName: 'calendar-plus', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddDate();
      props.onClose();
    },
  },
  {
    label: 'Add Past Gift',
    iconName: 'gift', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddGift();
      props.onClose();
    },
  },
  {
    label: 'Add New Profile',
    iconName: 'user-plus', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onAddProfile();
      props.onClose();
    },
  },
  {
    label: 'Edit Current Profile',
    iconName: 'edit-2', // Feather icon name
    action: (props: ActionMenuProps) => {
      props.onEditProfile();
      props.onClose();
    },
  },
];

// Define icon size once
const ICON_SIZE = 18;

const ActionMenu: React.FC<ActionMenuProps> = (props) => {
  const { className } = props;
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  
  const staggerDelay = 60;
  const animationDuration = 300;
  const initialOffsetY = -15; // Offset for entering/exiting animation

  // Theme-aware icon color
  const iconColor = isDark ? '#9CA3AF' : '#6B7280';

  return (
    <View
      // Using the provided className structure
      className={`absolute z-40 p-2 rounded-lg shadow-lg right-5 top-5 bg-card dark:bg-card-dark ${className}`}
    >
      {menuItems.map((item, index) => {
        // Define the Keyframe animation for ENTERING
        const enteringAnimation = new Keyframe({
          0: {
            opacity: 0,
            transform: [{ translateY: initialOffsetY }],
          },
          100: {
            opacity: 1,
            transform: [{ translateY: 0 }],
          },
        })
          .delay(index * staggerDelay) // Apply stagger delay based on index
          .duration(animationDuration); // Set animation duration

        // Define the Keyframe animation for EXITING
        const exitingAnimation = new Keyframe({
          0: {
            // Start state (fully visible)
            opacity: 1,
            transform: [{ translateY: 0 }],
          },
          100: {
            // End state (faded out and moved up/down)
            opacity: 0,
            transform: [{ translateY: initialOffsetY }], // Move back to initial offset
          },
        })
          // Apply REVERSE stagger delay for exiting
          .delay((menuItems.length - 1 - index) * staggerDelay)
          .duration(animationDuration); // Set animation duration

        return (
          <AnimatedTouchableOpacity
            key={item.label}
            entering={enteringAnimation}
            exiting={exitingAnimation}
            className="p-2"
            onPress={() => item.action(props)}
          >
            <View className="flex-row items-center gap-2">
              <Feather
                name={item.iconName as any}
                size={ICON_SIZE}
                color={iconColor}
              />

              <Text className="text-text-primary dark:text-text-primary-dark">
                {item.label}
              </Text>
            </View>
          </AnimatedTouchableOpacity>
        );
      })}
    </View>
  );
};

export default ActionMenu;
