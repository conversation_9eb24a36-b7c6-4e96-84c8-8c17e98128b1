import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { Feather } from '@expo/vector-icons';
import { useColorScheme } from 'nativewind';
import { SearchMode } from '../../hooks/useSearchRecommendations';

interface SearchModeToggleProps {
  searchMode: SearchMode;
  onToggle: (mode: SearchMode) => void;
  profileName?: string;
  disabled?: boolean;
}

const SearchModeToggle: React.FC<SearchModeToggleProps> = ({
  searchMode,
  onToggle,
  profileName,
  disabled = false,
}) => {
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const toggleMode = () => {
    if (!disabled) {
      onToggle(searchMode === 'generic' ? 'personalized' : 'generic');
    }
  };

  const isPersonalized = searchMode === 'personalized';

  return (
    <View className="mb-4">
      {/* Tap instruction */}
      <Text className="text-xs font-medium text-center mb-2 text-text-secondary dark:text-text-secondary-dark">
        {disabled ? 'Search Mode' : 'Tap to toggle search mode'}
      </Text>
      
      <TouchableOpacity
        onPress={toggleMode}
        disabled={disabled}
        className={`flex-row items-center justify-between p-4 rounded-xl border-2 transition-all duration-200 ${
          disabled 
            ? 'opacity-50 border-border dark:border-border-dark' 
            : isPersonalized
              ? 'border-primary/30 dark:border-primary-dark/30 shadow-sm active:shadow-md active:scale-[0.98]'
              : 'border-gray-300/50 dark:border-gray-600/50 shadow-sm active:shadow-md active:scale-[0.98]'
        } bg-card dark:bg-card-dark`}
        activeOpacity={0.8}
        accessibilityRole="switch"
        accessibilityState={{ 
          checked: isPersonalized,
          disabled: disabled
        }}
        accessibilityLabel={`Search mode toggle. Currently ${searchMode}${isPersonalized && profileName ? ` for ${profileName}` : ''}`}
      >
        <View className="flex-row items-center flex-1">
          <View className={`p-2 rounded-full ${
            isPersonalized 
              ? 'bg-primary/10 dark:bg-primary-dark/10' 
              : 'bg-gray-100 dark:bg-gray-700'
          }`}>
            <Feather
              name={isPersonalized ? 'user' : 'globe'}
              size={18}
              color={isPersonalized 
                ? (isDark ? '#C70039' : '#A3002B')
                : (isDark ? '#9CA3AF' : '#6B7280')
              }
            />
          </View>
          
          <View className="ml-3 flex-1">
            <Text className="font-semibold text-text-primary dark:text-text-primary-dark">
              {isPersonalized ? 'Personalized Search' : 'Generic Search'}
            </Text>
            <Text className="text-sm text-text-secondary dark:text-text-secondary-dark">
              {isPersonalized
                ? `Tailored for ${profileName || 'selected profile'}`
                : 'General gift recommendations'
              }
            </Text>
          </View>
        </View>
        
        {/* Toggle Switch */}
        <View className="flex-col items-center">
          <View
            className={`w-14 h-7 rounded-full p-0.5 transition-all duration-300 shadow-sm ${
              isPersonalized
                ? 'bg-primary dark:bg-primary-dark shadow-primary/20 dark:shadow-primary-dark/20'
                : 'bg-gray-300 dark:bg-gray-600'
            }`}
          >
            <View
              className={`w-6 h-6 rounded-full bg-white shadow-md transition-all duration-300 flex items-center justify-center ${
                isPersonalized ? 'translate-x-7' : 'translate-x-0'
              }`}
            >
              {/* Mini icon in toggle */}
              <Feather
                name={isPersonalized ? 'user' : 'globe'}
                size={10}
                color={isPersonalized 
                  ? (isDark ? '#C70039' : '#A3002B')
                  : (isDark ? '#6B7280' : '#9CA3AF')
                }
              />
            </View>
          </View>
          
          {/* Toggle state indicator */}
          {!disabled && (
            <View className="flex-row items-center mt-1">
              <View className={`w-1 h-1 rounded-full mx-0.5 transition-all duration-200 ${
                !isPersonalized ? 'bg-gray-400' : 'bg-gray-200'
              }`} />
              <View className={`w-1 h-1 rounded-full mx-0.5 transition-all duration-200 ${
                isPersonalized ? 'bg-primary dark:bg-primary-dark' : 'bg-gray-200'
              }`} />
            </View>
          )}
        </View>
      </TouchableOpacity>

      {/* Helper Text */}
      {disabled && (
        <Text className="text-xs mt-2 text-center text-text-secondary dark:text-text-secondary-dark">
          Select a profile to enable personalized search
        </Text>
      )}
    </View>
  );
};

export default SearchModeToggle; 