import React from 'react';
import { View, Text, TouchableOpacity, Dimensions, Image } from 'react-native';
import Animated, { SlideInUp } from 'react-native-reanimated';
import { useRouter } from 'expo-router';
import { useColorScheme } from 'nativewind';
import * as Haptics from 'expo-haptics';
import { SignificantOtherProfile } from '../../functions/src/types/firestore';
import allprofilesicon from '@/assets/images/allprofiles.png'
interface NavigationGridProps {
  selectedProfileId: string | null;
  selectedProfile?: SignificantOtherProfile | null;
  onNavigate?: (route: string) => void;
  onOpenNoteModal?: () => void;
  onOpenDateModal?: () => void;
  onOpenGiftModal?: () => void;
}

interface NavigationCard {
  id: string;
  title: string;
  subtitle: string;
  icon: number; // Now only expects image imports
  iconLibrary: 'image'; // Only image icons
  route: string;
  color: string;
  requiresProfile: boolean;
}

const navigationCards: NavigationCard[] = [
  {
    id: 'profiles',
    title: 'All Profiles',
    subtitle: 'Manage gift profiles',
    icon: allprofilesicon,
    iconLibrary: 'image',
    route: '/profiles',
    color: '#A3002B',
    requiresProfile: false,
  },
  {
    id: 'profile-details',
    title: 'Profile Details',
    subtitle: 'View current profile',
    icon: allprofilesicon,
    iconLibrary: 'image',
    route: '/profiles/[profileId]',
    color: '#E5355F',
    requiresProfile: true,
  },
  {
    id: 'notes',
    title: 'Notes',
    subtitle: 'Personal insights',
    icon: allprofilesicon,
    iconLibrary: 'image',
    route: '/profiles/[profileId]/notes',
    color: '#E87900',
    requiresProfile: true,
  },
  {
    id: 'dates',
    title: 'Important Dates',
    subtitle: 'Special occasions',
    icon: allprofilesicon,
    iconLibrary: 'image',
    route: '/profiles/[profileId]/dates',
    color: '#16A34A',
    requiresProfile: true,
  },
  {
    id: 'past-gifts',
    title: 'Past Gifts',
    subtitle: 'Gift history',
    icon: allprofilesicon,
    iconLibrary: 'image',
    route: '/profiles/[profileId]/past-gifts',
    color: '#7C3AED',
    requiresProfile: true,
  },
  {
    id: 'feedback',
    title: 'Liked & Disliked',
    subtitle: 'Gift preferences',
    icon: allprofilesicon,
    iconLibrary: 'image',
    route: '/profiles/[profileId]/feedback',
    color: '#DC2626',
    requiresProfile: true,
  },
];

const NavigationGrid: React.FC<NavigationGridProps> = ({
  selectedProfileId,
  selectedProfile,
  onNavigate,
  onOpenNoteModal,
  onOpenDateModal,
  onOpenGiftModal
}) => {
  const router = useRouter();
  const { colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const screenWidth = Dimensions.get('window').width;
  const cardWidth = (screenWidth - 60) / 2; // Account for padding and gap

  const COMMON_ICON_SIZE = 48; // Define a common size for all icons

  // Helper function to check if a section is empty
  const isSectionEmpty = (sectionId: string): boolean => {
    if (!selectedProfile) return true;

    switch (sectionId) {
      case 'notes':
        return !selectedProfile.generalNotes || selectedProfile.generalNotes.length === 0;
      case 'dates':
        return !selectedProfile.customDates || selectedProfile.customDates.length === 0;
      case 'past-gifts':
        return !selectedProfile.pastGiftsGiven || selectedProfile.pastGiftsGiven.length === 0;
      default:
        return false;
    }
  };

  const handleCardPress = (card: NavigationCard) => {
    if (card.requiresProfile && !selectedProfileId) {
      // Could show a toast or alert here
      return;
    }

    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    // Check for empty states and trigger modals instead of navigation
    if (selectedProfile && isSectionEmpty(card.id)) {
      switch (card.id) {
        case 'notes':
          if (onOpenNoteModal) {
            onOpenNoteModal();
            return;
          }
          break;
        case 'dates':
          if (onOpenDateModal) {
            onOpenDateModal();
            return;
          }
          break;
        case 'past-gifts':
          if (onOpenGiftModal) {
            onOpenGiftModal();
            return;
          }
          break;
      }
    }

    // Default navigation behavior
    let route = card.route;
    if (card.requiresProfile && selectedProfileId) {
      route = route.replace('[profileId]', selectedProfileId);
    }

    if (onNavigate) {
      onNavigate(route);
    } else {
      router.push(route as any);
    }
  };

  const renderIcon = (card: NavigationCard) => {
    return <Image source={card.icon} className="rounded-full" style={{ width: COMMON_ICON_SIZE, height: COMMON_ICON_SIZE }} />;
  };

  return (
    <View className="mb-6">
      <Text className="mb-4 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
        Quick Actions
      </Text>

      <View className="flex-row flex-wrap justify-between">
        {navigationCards.map((card, index) => {
          const isDisabled = card.requiresProfile && !selectedProfileId;
          const isEmpty = selectedProfile && isSectionEmpty(card.id);
          const showEmptyIndicator = isEmpty && ['notes', 'dates', 'past-gifts'].includes(card.id);

          return (
            <Animated.View
              key={card.id}
              entering={SlideInUp.delay(index * 100).duration(400)}
              style={{ width: cardWidth }}
              className="mb-4"
            >
              <TouchableOpacity
                onPress={() => handleCardPress(card)}
                disabled={isDisabled}
                className={`
                  p-4 rounded-xl border shadow-sm relative
                  ${isDisabled
                    ? 'bg-gray-100 border-gray-200 opacity-50 dark:bg-gray-800 dark:border-gray-700'
                    : showEmptyIndicator
                    ? 'bg-primary/5 dark:bg-primary-dark/10 border-primary/20 dark:border-primary-dark/20'
                    : 'bg-card dark:bg-card-dark border-border dark:border-border-dark'
                  }
                  active:scale-95
                `}
                activeOpacity={0.7}
              >
                <View className="items-center">
                  {renderIcon(card)}

                  <Text
                    className={`
                      text-sm font-semibold text-center mb-1
                      ${isDisabled
                        ? 'text-gray-400 dark:text-gray-600'
                        : 'text-text-primary dark:text-text-primary-dark'
                      }
                    `}
                    numberOfLines={2}
                  >
                    {card.title}
                  </Text>

                  <Text
                    className={`
                      text-xs text-center
                      ${isDisabled
                        ? 'text-gray-400 dark:text-gray-600'
                        : showEmptyIndicator
                        ? 'text-primary dark:text-primary-dark'
                        : 'text-text-secondary dark:text-text-secondary-dark'
                      }
                    `}
                    numberOfLines={2}
                  >
                    {showEmptyIndicator ? 'Tap to add first item' : card.subtitle}
                  </Text>
                </View>

                
              </TouchableOpacity>
            </Animated.View>
          );
        })}
      </View>
    </View>
  );
};

export default NavigationGrid;
