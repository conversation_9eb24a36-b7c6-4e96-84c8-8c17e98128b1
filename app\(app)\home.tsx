// ~/app/(app)/home.tsx
import { AddGeneralNoteModal } from '../../components/profile/AddGeneralNoteModal';
import { v4 as uuidv4 } from 'uuid'; // LOW 6: Use uuid for temporary IDs
import { Timestamp } from 'firebase/firestore';
import { updateSignificantOther } from '../../services/profileService';
import React, {
  useEffect,
  useState,
  useRef,
  useCallback,
  useMemo,
} from 'react';
import {
  View,
  Text,
  Pressable,
  Modal,
  SafeAreaView,
  ScrollView,
  // Image, // Not used after removing commented-out logo
  TouchableOpacity,
  Alert, // For user feedback
} from 'react-native';
import {
  useFocusEffect,
  // useNavigation, // Not directly used in this snippet, router is used
  useRouter,
} from 'expo-router';
import { Feather } from '@expo/vector-icons';
import Animated, {
  FadeIn,
  // FadeOut, // Not explicitly used in this snippet
  SlideInRight,
  useAnimatedStyle,
  useSharedValue,
  withSequence,
  withTiming,
} from 'react-native-reanimated';
import { Link } from 'expo-router';
import * as Haptics from 'expo-haptics';
import Button from '../../components/ui/Button';
import LoadingIndicator from '../../components/ui/LoadingIndicator';
import Card from '../../components/ui/Card';
import { useAuth } from '../../contexts/AuthContext';
import {
  fetchGiftRecommendations,
  saveRecommendationFeedback,
  getProfileFeedback,
  deleteRecommendationFeedback,
  type FeedbackEntry, // Assuming FeedbackEntry type is defined
  type GiftRecommendation, // Assuming GiftRecommendation type is defined
} from '../../services/recommendationService';
import { SignificantOtherProfile } from '../../functions/src/types/firestore'; // Adjust path if needed
import AsyncStorage from '@react-native-async-storage/async-storage';
import ActionMenu from '../../components/home/<USER>';
import MotivationalHeader from '../../components/home/<USER>';
import NavigationGrid from '../../components/home/<USER>';
import UpcomingDatesDisplay from '../../components/home/<USER>';
import AddCustomDateModal from '../../components/profile/AddCustomDateModal';
import AddPastGiftModal from '../../components/profile/AddPastGiftModal';
import ProfileCompletionBanner from '../../components/home/<USER>';
import KeyDatesDisplay from '../../components/home/<USER>';
import useCalendarData from '../../hooks/useCalendarData';
import { useColorScheme } from 'nativewind'; // Or useThemeManager
import { 
  PROFILES_LAST_UPDATED_KEY,
  SELECTED_PROFILE_KEY,
  PROFILES_CACHE_KEY,
  CACHE_EXPIRY_KEY 
} from '../../constants/storageKeys';

const AnimatedCard = Animated.createAnimatedComponent(Card);
const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

const CACHE_EXPIRY_TIME = 1000 * 60 * 30; // 30 minutes

export default function HomeScreen() {
  // const navigation = useNavigation(); // Not used
  const router = useRouter();
  const { user } = useAuth();
  const { colorScheme } = useColorScheme(); // Using NativeWind's hook
  const isDark = colorScheme === 'dark';

  // Get calendar data and profiles from the hook instead of fetching separately
  const { upcomingDates, profiles, selectedProfileId: calendarSelectedProfileId, handleProfileSelect: calendarHandleProfileSelect, isLoading: calendarLoading } = useCalendarData();

  // Use profiles from useCalendarData instead of separate state
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(calendarSelectedProfileId);
  const [showDropdown, setShowDropdown] = useState<boolean>(false);
  const [isMenuVisible, setIsMenuVisible] = useState(false);
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [isDateModalVisible, setIsDateModalVisible] = useState(false);
  const [isGiftModalVisible, setIsGiftModalVisible] = useState(false);

  const [loading, setLoading] = useState<boolean>(false); // Only for recommendations loading
  const [error, setError] = useState<string | null>(null); // Main data error

  const [recommendations, setRecommendations] = useState<
    GiftRecommendation[] | null
  >(null);
  const [recommendationsLoading, setRecommendationsLoading] =
    useState<boolean>(false);
  const [recommendationsError, setRecommendationsError] = useState<
    string | null
  >(null);

  const [currentFeedbackMap, setCurrentFeedbackMap] = useState<
    Map<string, FeedbackEntry>
  >(new Map());
  const [feedbackError, setFeedbackError] = useState<string | null>(null); // MEDIUM 1: Error state for feedback
  const processingFeedbackRef = useRef(new Set<string>()); // For feedback race condition (fix is present)

  const [dataInitialized, setDataInitialized] = useState(false);

  const dropdownScale = useSharedValue(1); // Not used in current JSX
  const headerOpacity = useSharedValue(0);
  const plusIconRotation = useSharedValue(0);
  const backdropOpacity = useSharedValue(0);
  // const flatListRef = useRef(null); // Not used

  // Sync selectedProfileId with calendar hook
  useEffect(() => {
    if (calendarSelectedProfileId !== selectedProfileId) {
      setSelectedProfileId(calendarSelectedProfileId);
      // Clear recommendations when profile changes
      if (calendarSelectedProfileId !== selectedProfileId) {
        setRecommendations(null);
        setCurrentFeedbackMap(new Map());
        if (calendarSelectedProfileId) {
          fetchRecommendations(calendarSelectedProfileId);
          fetchProfileFeedback(calendarSelectedProfileId);
        }
      }
    }
  }, [calendarSelectedProfileId, selectedProfileId]);

  // Initialize data when component mounts
  useEffect(() => {
    if (selectedProfileId && !dataInitialized) {
      fetchRecommendations(selectedProfileId);
      fetchProfileFeedback(selectedProfileId);
      setDataInitialized(true);
      
      headerOpacity.value = withSequence(
        withTiming(0, { duration: 0 }),
        withTiming(1, { duration: 800 })
      );
    }
  }, [selectedProfileId, dataInitialized]);

  useEffect(() => {
    plusIconRotation.value = withTiming(isMenuVisible ? 45 : 0, {
      duration: 200,
    });
    backdropOpacity.value = withTiming(isMenuVisible ? 0.5 : 0, {
      duration: 200,
    });
  }, [isMenuVisible, plusIconRotation, backdropOpacity]);

  const fetchRecommendations = useCallback(async (profileId: string) => {
    if (!profileId) return;
    setRecommendationsLoading(true);
    setRecommendationsError(null);
    setRecommendations(null); // Clear old recommendations
    try {
      const result = await fetchGiftRecommendations(profileId);
      setRecommendations(result || []);
    } catch (err: any) {
      setRecommendationsError(
        'Failed to load recommendations. Please try again.'
      );
      setRecommendations(null);
    } finally {
      setRecommendationsLoading(false);
    }
  }, []); // Removed dependencies that were causing re-creation

  // MEDIUM 1: Modified fetchProfileFeedback
  const fetchProfileFeedback = useCallback(async (profileId: string) => {
    if (!profileId) return;

    setFeedbackError(null); // Reset feedback error
    try {
      const feedback = await getProfileFeedback(profileId);
      const feedbackMap = new Map<string, FeedbackEntry>();
      feedback.forEach((entry) => {
        if (entry.recommendationId) {
          // Ensure recommendationId exists
          feedbackMap.set(entry.recommendationId, entry);
        }
      });
      setCurrentFeedbackMap(feedbackMap);
    } catch (error) {
      setFeedbackError('Could not load feedback data.'); // Set user-facing error
      setCurrentFeedbackMap(new Map()); // Clear feedback map on error
    }
  }, []); // Empty dependency array if it doesn't depend on component state/props

  const dropdownAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: dropdownScale.value }],
  }));
  const headerAnimatedStyle = useAnimatedStyle(() => ({
    opacity: headerOpacity.value,
  }));
  const backdropAnimatedStyle = useAnimatedStyle(() => ({
    opacity: backdropOpacity.value,
    pointerEvents: isMenuVisible ? 'auto' : 'none',
  }));
  const plusIconAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ rotate: `${plusIconRotation.value}deg` }],
  }));

  const handleProfileSelect = useCallback(
    async (profileId: string) => {
      // Use the calendar hook's profile selection
      await calendarHandleProfileSelect(profileId);
      setShowDropdown(false);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    },
    [calendarHandleProfileSelect]
  );

  const handleDropdownPress = () => setShowDropdown(true);

  const handleGeneratePress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    if (selectedProfileId) fetchRecommendations(selectedProfileId);
  };

  // handleFeedback remains largely the same but uses processingFeedbackRef (already present)
  // LOW 6: Changed temporary ID to use uuidv4
  const handleFeedback = async (
    item: GiftRecommendation,
    feedbackType: 'like' | 'dislike'
  ) => {
    // Use recommendationId if id is not available (fix for the actual issue)
    const recommendationId = item.id || item.recommendationId;

    if (
      !user?.uid ||
      !selectedProfileId ||
      !recommendationId ||
      typeof recommendationId !== 'string'
    ) {
      return;
    }
    if (
      recommendationId &&
      processingFeedbackRef.current.has(recommendationId)
    ) {
      return;
    }

    if (recommendationId) {
      processingFeedbackRef.current.add(recommendationId);
    }
    const existingFeedback = recommendationId
      ? currentFeedbackMap.get(recommendationId)
      : undefined;
    const newFeedbackMap = new Map(currentFeedbackMap);

    try {
      if (existingFeedback?.feedbackType === feedbackType) {
        // Same feedback type clicked - remove it (toggle off)
        if (recommendationId) {
          newFeedbackMap.delete(recommendationId);
        }
        setCurrentFeedbackMap(newFeedbackMap);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        if (existingFeedback.id) {
          await deleteRecommendationFeedback(existingFeedback.id);
        }
      } else {
        // Different feedback type clicked - replace the existing one
        if (recommendationId) {
          // First delete the existing feedback if it exists
          if (existingFeedback?.id) {
            await deleteRecommendationFeedback(existingFeedback.id);
          }

          const tempFeedback: FeedbackEntry = {
            id: uuidv4(), // LOW 6: Use uuid for temp client-side ID
            userId: user.uid,
            profileId: selectedProfileId,
            recommendationId,
            feedbackType,
            recommendationDetails: {
              name: item.name,
              description: item.description,
            },
            timestamp: new Date(), // Or Timestamp.now() if FeedbackEntry expects Timestamp
          };
          newFeedbackMap.set(recommendationId, tempFeedback);
          setCurrentFeedbackMap(newFeedbackMap);

          Haptics.impactAsync(
            feedbackType === 'like'
              ? Haptics.ImpactFeedbackStyle.Light
              : Haptics.ImpactFeedbackStyle.Medium
          );

          const success = await saveRecommendationFeedback({
            userId: user.uid,
            profileId: selectedProfileId,
            recommendationId,
            feedbackType,
            recommendationDetails: {
              name: item.name,
              description: item.description,
            },
          });
          if (!success) throw new Error('Save feedback failed');

          await fetchProfileFeedback(selectedProfileId); // Refetch to get actual IDs
        }
      }
    } catch (err) {
      // Revert optimistic update
      if (existingFeedback && recommendationId) {
        newFeedbackMap.set(recommendationId, existingFeedback);
      } else if (recommendationId) {
        newFeedbackMap.delete(recommendationId);
      }
      setCurrentFeedbackMap(newFeedbackMap);
      Alert.alert('Error', 'Could not save your feedback. Please try again.');
    } finally {
      if (recommendationId) {
        processingFeedbackRef.current.delete(recommendationId);
      }
    }
  };

  const handleNavigateToAddProfile = () => {
    router.push('/profiles/add');
    setIsMenuVisible(false);
  };
  const handleNavigateToEditProfile = () => {
    if (selectedProfileId) {
      router.push(`/profiles/${selectedProfileId}/edit`);
    } else {
      Alert.alert('No Profile Selected', 'Please select a profile to edit.'); // LOW 1: User feedback
    }
    setIsMenuVisible(false);
  };
  const handleOpenNoteModal = () => {
    setIsNoteModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleOpenDateModal = () => {
    setIsDateModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleOpenGiftModal = () => {
    setIsGiftModalVisible(true);
    setIsMenuVisible(false);
  };

  const handleSaveNote = async (noteText: string) => {
    if (!user?.uid || !selectedProfileId || !selectedProfile) {
      Alert.alert('Error', 'Cannot save note. Ensure a profile is selected.');
      return;
    }
    try {
      const newNote = { id: uuidv4(), note: noteText, date: Timestamp.now() };
      const updatedNotes = [...(selectedProfile.generalNotes || []), newNote];

      // No optimistic update needed - useCalendarData will handle the refresh

      // Save to backend
      await updateSignificantOther(user.uid, selectedProfileId, {
        generalNotes: updatedNotes,
      });

      setIsNoteModalVisible(false);

      // Update AsyncStorage timestamp to trigger other components to refresh
      await AsyncStorage.setItem(
        PROFILES_LAST_UPDATED_KEY,
        Date.now().toString()
      );
    } catch (err) {
      // Error handled, useCalendarData will refresh on focus
      Alert.alert('Error', 'Could not save note. Please try again.');
    }
  };

  const handleSaveDate = async (dateData: {
    name: string;
    date: Date | null;
    profileId: string | null;
  }) => {
    if (
      !user?.uid ||
      !selectedProfileId ||
      !selectedProfile ||
      !dateData.date
    ) {
      Alert.alert(
        'Error',
        'Cannot save date. Ensure a profile is selected and date is provided.'
      );
      return;
    }
    try {
      const newDate = {
        id: uuidv4(),
        name: dateData.name,
        type: 'Custom',
        date: Timestamp.fromDate(dateData.date),
      };
      const updatedDates = [...(selectedProfile.customDates || []), newDate];

      // No optimistic update needed - useCalendarData will handle the refresh

      // Save to backend
      await updateSignificantOther(user.uid, selectedProfileId, {
        customDates: updatedDates,
      });

      setIsDateModalVisible(false);

      // Update AsyncStorage timestamp to trigger other components to refresh
      await AsyncStorage.setItem(
        PROFILES_LAST_UPDATED_KEY,
        Date.now().toString()
      );
    } catch (err) {
      // Error handled, useCalendarData will refresh on focus
      Alert.alert('Error', 'Could not save date. Please try again.');
    }
  };

  const handleSaveGift = async (giftData: {
    item: string;
    occasion?: string;
    date: Date | null;
    reaction?: string;
  }) => {
    if (!user?.uid || !selectedProfileId || !selectedProfile) {
      Alert.alert('Error', 'Cannot save gift. Ensure a profile is selected.');
      return;
    }
    try {
      const newGift = {
        item: giftData.item,
        occasion: giftData.occasion,
        date: giftData.date
          ? Timestamp.fromDate(giftData.date)
          : Timestamp.now(),
        reaction: giftData.reaction,
      };
      const updatedGifts = [...(selectedProfile.pastGiftsGiven || []), newGift];

      // No optimistic update needed - useCalendarData will handle the refresh

      // Save to backend
      await updateSignificantOther(user.uid, selectedProfileId, {
        pastGiftsGiven: updatedGifts,
      });

      setIsGiftModalVisible(false);

      // Update AsyncStorage timestamp to trigger other components to refresh
      await AsyncStorage.setItem(
        PROFILES_LAST_UPDATED_KEY,
        Date.now().toString()
      );
    } catch (err) {
      // Error handled, useCalendarData will refresh on focus
      Alert.alert('Error', 'Could not save gift. Please try again.');
    }
  };

  const selectedProfile = useMemo(
    () => profiles?.find((p) => p.profileId === selectedProfileId),
    [profiles, selectedProfileId]
  );

  // Sync selectedProfileId with calendar hook
  useEffect(() => {
    if (calendarSelectedProfileId !== selectedProfileId) {
      setSelectedProfileId(calendarSelectedProfileId);
      // Clear recommendations when profile changes
      if (calendarSelectedProfileId !== selectedProfileId) {
        setRecommendations(null);
        setCurrentFeedbackMap(new Map());
        if (calendarSelectedProfileId) {
          fetchRecommendations(calendarSelectedProfileId);
          fetchProfileFeedback(calendarSelectedProfileId);
        }
      }
    }
  }, [calendarSelectedProfileId, selectedProfileId]);

  // Initialize data when component mounts
  useEffect(() => {
    if (selectedProfileId && !dataInitialized) {
      fetchRecommendations(selectedProfileId);
      fetchProfileFeedback(selectedProfileId);
      setDataInitialized(true);
      
      headerOpacity.value = withSequence(
        withTiming(0, { duration: 0 }),
        withTiming(1, { duration: 800 })
      );
    }
  }, [selectedProfileId, dataInitialized]);

  // --- Render Logic ---
  if (calendarLoading) {
    // Show initial full screen loader while calendar data loads
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <View className="flex-1 justify-center items-center">
          <Text className="mb-6 text-xl font-semibold text-primary dark:text-primary-dark">
            Loading your profiles...
          </Text>
          <LoadingIndicator
            color={isDark ? '#C70039' : '#A3002B'}
            size="large"
          />
        </View>
      </SafeAreaView>
    );
  }

  if (!profiles || (profiles.length === 0 && !calendarLoading)) {
    // Show empty state if no profiles after loading
    return (
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <Animated.View
          entering={FadeIn.duration(600)}
          className="flex-1 justify-center items-center px-6"
        >
          <View className="p-8 w-full max-w-sm rounded-2xl shadow-md bg-card dark:bg-card-dark">
            <Text className="mb-3 text-2xl font-bold text-center text-primary dark:text-primary-dark">
              Welcome to Giftmi!
            </Text>
            <Text className="mb-8 text-base text-center text-text-secondary dark:text-text-secondary-dark">
              Create a gift profile to get personalized recommendations
            </Text>
            <Link href="/profiles/add" asChild>
              <Button
                title="Create a Gift Profile"
                variant="primary"
                className="mb-4 w-full"
                onPress={() =>
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium)
                }
                leftIcon={
                  <Feather name="plus-circle" size={18} color="white" />
                }
              />
            </Link>

            {error && (
              <Animated.Text
                entering={FadeIn.delay(300)}
                className="mt-4 text-sm text-center text-error dark:text-error-dark"
              >
                {error}
              </Animated.Text>
            )}
          </View>
        </Animated.View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
      <View className="relative flex-1 px-5 py-6">
        <Animated.View
          style={backdropAnimatedStyle}
          className="absolute inset-0 z-20 bg-black"
          onTouchEnd={() => setIsMenuVisible(false)}
        />
        <View className="flex-row justify-between items-center mb-8">
          <Animated.View style={headerAnimatedStyle}>
            <Text className="text-2xl font-bold text-primary dark:text-primary-dark">
              Giftmi
            </Text>
          </Animated.View>
          {profiles.length === 1 && selectedProfile ? (
            <View className="p-2 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark">
              <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                {selectedProfile.name}
              </Text>
            </View>
          ) : (
            <View className="relative w-1/2">
              <AnimatedPressable
                className="flex-row justify-between items-center p-2 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark"
                style={[dropdownAnimatedStyle]}
                onPress={handleDropdownPress}
                accessibilityRole="button"
                accessibilityLabel="Select profile"
              >
                <Text className="text-text-secondary dark:text-text-secondary-dark">
                  Profile:{' '}
                </Text>
                <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                  {selectedProfile?.name || 'Select Profile'}
                </Text>
                <Feather
                  name="chevron-down"
                  size={20}
                  color={isDark ? '#C70039' : '#A3002B'}
                />
              </AnimatedPressable>
              <Modal
                visible={showDropdown}
                transparent
                animationType="fade"
                onRequestClose={() => setShowDropdown(false)}
              >
                <Pressable
                  className="absolute inset-0 bg-black/40"
                  onPress={() => setShowDropdown(false)}
                />
                <Animated.View
                  entering={FadeIn.duration(200)}
                  className="absolute right-0 bottom-0 left-0 rounded-t-2xl shadow-lg bg-card dark:bg-card-dark"
                >
                  <View className="flex items-center pt-3 pb-2">
                    <View className="w-16 h-1 rounded-full bg-border dark:bg-border-dark" />
                  </View>
                  <Text className="px-5 py-3 text-lg font-semibold text-text-primary dark:text-text-primary-dark">
                    Select a profile
                  </Text>
                  {profiles.map((profile) => (
                    <Pressable
                      key={profile.profileId}
                      className={`p-4 border-b border-border dark:border-border-dark ${
                        profile.profileId === selectedProfileId
                          ? 'bg-primary/5 dark:bg-primary-dark/10'
                          : ''
                      }`}
                      onPress={() => handleProfileSelect(profile.profileId)}
                      accessibilityRole="button"
                      accessibilityLabel={`Select ${profile.name}`}
                      accessibilityState={{
                        selected: profile.profileId === selectedProfileId,
                      }}
                    >
                      <Text
                        className={`text-base ${
                          profile.profileId === selectedProfileId
                            ? 'font-semibold text-primary dark:text-primary-dark'
                            : 'text-text-primary dark:text-text-primary-dark'
                        }`}
                      >
                        {profile.name}
                      </Text>
                    </Pressable>
                  ))}
                  <SafeAreaView>
                    <View className="p-5">
                      <Button
                        title="Close"
                        variant="secondary"
                        onPress={() => setShowDropdown(false)}
                        className="w-full"
                      />
                    </View>
                  </SafeAreaView>
                </Animated.View>
              </Modal>
            </View>
          )}
          <TouchableOpacity
            onPress={() => setIsMenuVisible((prev) => !prev)}
            testID="plus-icon-button"
          >
            <Animated.View style={plusIconAnimatedStyle}>
              <Feather
                name="plus"
                size={28}
                color={isDark ? '#C70039' : '#A3002B'}
              />
            </Animated.View>
          </TouchableOpacity>
        </View>

        {isMenuVisible && (
          <ActionMenu
            className="absolute right-1 top-20 z-40"
            onClose={() => setIsMenuVisible(false)}
            onAddProfile={handleNavigateToAddProfile}
            onAddNote={handleOpenNoteModal}
            onAddDate={handleOpenDateModal}
            onAddGift={handleOpenGiftModal}
            onEditProfile={handleNavigateToEditProfile}
          />
        )}

        {feedbackError && (
          <Animated.View
            entering={FadeIn.duration(300)}
            className="p-3 mb-4 rounded-md bg-error/20 dark:bg-error-dark/20"
          >
            <Text className="text-sm text-center text-error dark:text-error-dark">
              {feedbackError}
            </Text>
          </Animated.View>
        )}

        <ScrollView
          className="flex-1"
          showsVerticalScrollIndicator={false}
          contentContainerStyle={{ paddingBottom: 20 }}
        >
          
          <View className="px-0">
            
            <MotivationalHeader 
              userName={user?.displayName || undefined}
              upcomingDatesCount={upcomingDates.length}
              profileCompletionCount={profiles.filter(profile => {
                // Basic completion check - profile has name, basic info, and at least some preferences
                return profile.name && 
                       (profile.birthday || profile.anniversary || profile.relationship) &&
                       (profile.interests?.length > 0 || profile.dislikes?.length > 0 || profile.wishlistItems?.length > 0);
              }).length}
              totalProfiles={profiles.length}
              enableEnhancedMessaging={true}
              selectedProfileName={profiles.find(p => p.profileId === selectedProfileId)?.name}
            />

            
            <ProfileCompletionBanner profile={selectedProfile || null} />

            
            <NavigationGrid
              selectedProfileId={selectedProfileId}
              selectedProfile={selectedProfile}
              onOpenNoteModal={handleOpenNoteModal}
              onOpenDateModal={handleOpenDateModal}
              onOpenGiftModal={handleOpenGiftModal}
            />

            
            <KeyDatesDisplay profile={selectedProfile || null} />

            
            {selectedProfileId && (
              <UpcomingDatesDisplay
                upcomingDates={upcomingDates.filter(
                  (date) =>
                    date.type !== 'Birthday' && date.type !== 'Anniversary'
                )}
                onAddDatePress={handleOpenDateModal}
              />
            )}
          </View>

          
          <View className="mx-auto w-full max-w-sm">
            
            <View className="flex-row justify-between items-center mb-4">
              <Text className="text-xl font-bold text-text-primary dark:text-text-primary-dark">
                Daily Gift Recommendations
              </Text>
              {recommendations &&
                recommendations.length > 0 &&
                recommendationsLoading && (
                  <View className="flex-row items-center">
                    <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="small" />
                    <Text className="ml-2 text-sm text-text-secondary dark:text-text-secondary-dark">
                      Updating...
                    </Text>
                  </View>
                )}
            </View>

            {recommendationsLoading && !recommendations?.length ? ( // Show loader if loading AND no recommendations yet
              <View className="justify-center items-center py-16">
                <LoadingIndicator color={isDark ? '#C70039' : '#A3002B'} size="large" />
                <Text className="mt-6 text-base font-medium text-text-secondary dark:text-text-secondary-dark">
                  Finding perfect gifts...
                </Text>
              </View>
            ) : recommendationsError ? (
              <Animated.View
                entering={FadeIn.duration(300)}
                className="justify-center items-center py-16"
              >
                <View className="justify-center items-center mb-6 w-20 h-20 rounded-full bg-error/10 dark:bg-error-dark/10">
                  <Feather
                    name="alert-circle"
                    size={32}
                    color={isDark ? '#B20021' : '#D90429'}
                  />
                </View>
                <Text className="mb-6 text-lg font-medium text-center text-error dark:text-error-dark">
                  {recommendationsError}
                </Text>
                <Button
                  title="Try Again"
                  variant="secondary"
                  className="px-6"
                  onPress={handleGeneratePress}
                />
              </Animated.View>
            ) : recommendations && recommendations.length > 0 ? (
              <View>
                <Animated.View
                  className={`${
                    recommendationsLoading ? 'opacity-60' : 'opacity-100'
                  }`}
                >
                  {recommendations.map((item, index) => (
                    <AnimatedCard
                      key={item.id}
                      entering={SlideInRight.delay(index * 100).springify()}
                      className="overflow-hidden mb-4 rounded-xl border shadow-sm bg-card dark:bg-card-dark border-border dark:border-border-dark"
                    >
                      <View className="p-5">
                        <Text className="text-lg font-semibold text-primary dark:text-primary-dark">
                          {item.name}
                        </Text>
                        <Text className="mt-2 text-base text-text-secondary dark:text-text-secondary-dark">
                          {item.description}
                        </Text>
                        <View className="flex-row flex-wrap mt-4">
                          {item.priceRange && (
                            <View className="px-3 py-1 mr-2 mb-2 rounded-full bg-primary/10 dark:bg-primary-dark/10">
                              <Text className="text-xs font-medium text-primary dark:text-primary-dark">
                                {item.priceRange}
                              </Text>
                            </View>
                          )}
                          {item.categories?.map(
                            (category: string, i: number) => (
                              <View
                                key={i}
                                className="px-3 py-1 mr-2 mb-2 rounded-full bg-accent/10 dark:bg-accent-dark/10"
                              >
                                <Text className="text-xs font-medium text-accent dark:text-accent-dark">
                                  {category}
                                </Text>
                              </View>
                            )
                          )}
                        </View>
                        <View className="flex-row gap-10 justify-center items-center pt-3 mt-3 border-t border-border/50 dark:border-border-dark/50">
                          <Pressable
                            className={`p-3 rounded-full flex flex-row items-center gap-2 ${
                              currentFeedbackMap.get(item.id)?.feedbackType ===
                              'dislike'
                                ? 'bg-error/20 dark:bg-error-dark/20'
                                : 'active:bg-error/10'
                            }`}
                            onPress={() => handleFeedback(item, 'dislike')}
                            accessibilityLabel="Dislike this recommendation"
                            accessibilityRole="button"
                            disabled={processingFeedbackRef.current.has(
                              item.id
                            )}
                            accessibilityState={{
                              selected:
                                currentFeedbackMap.get(item.id)
                                  ?.feedbackType === 'dislike',
                            }}
                          >
                            <Feather
                              name="minus"
                              size={22}
                              color={isDark ? '#B20021' : '#D90429'}
                            />{' '}
                            <Text>Dislike</Text>
                          </Pressable>
                          <Pressable
                            className={`p-3 rounded-full flex flex-row items-center gap-2 ${
                              currentFeedbackMap.get(item.id)?.feedbackType ===
                              'like'
                                ? 'bg-primary/20 dark:bg-primary-dark/20'
                                : 'active:bg-primary/10'
                            }`}
                            onPress={() => handleFeedback(item, 'like')}
                            accessibilityLabel="Like this recommendation"
                            accessibilityRole="button"
                            disabled={processingFeedbackRef.current.has(
                              item.id
                            )}
                            accessibilityState={{
                              selected:
                                currentFeedbackMap.get(item.id)
                                  ?.feedbackType === 'like',
                            }}
                          >
                            <Text>Like</Text>{' '}
                            <Feather
                              name="plus"
                              size={22}
                              color={isDark ? '#C70039' : '#A3002B'}
                            />
                          </Pressable>
                        </View>
                      </View>
                    </AnimatedCard>
                  ))}
                </Animated.View>
                {selectedProfileId && (
                  <Button
                    title={
                      recommendationsLoading
                        ? 'Finding Gifts...'
                        : 'Generate New Ideas'
                    }
                    variant="primary"
                    className="mt-5 w-full"
                    onPress={handleGeneratePress}
                    disabled={!selectedProfileId || recommendationsLoading}
                    isLoading={recommendationsLoading}
                  />
                )}
              </View>
            ) : (
              <View className="justify-center items-center py-16">
                <View className="justify-center items-center mb-6 w-20 h-20 rounded-full bg-primary/10 dark:bg-primary-dark/10">
                  <Feather
                    name="gift"
                    size={32}
                    color={isDark ? '#C70039' : '#A3002B'}
                  />
                </View>
                <Text className="text-lg text-center text-text-secondary dark:text-text-secondary-dark">
                  {selectedProfileId
                    ? 'No recommendations yet. Generate some gift ideas!'
                    : 'Select a profile to see recommendations'}
                </Text>
                {feedbackError && (
                  <Text className="mt-4 text-sm text-center text-error dark:text-error-dark">
                    {feedbackError}
                  </Text>
                )}
                {selectedProfileId && (
                  <Button
                    title={
                      recommendationsLoading
                        ? 'Finding Gifts...'
                        : 'Generate New Ideas'
                    }
                    variant="primary"
                    className="mt-6 w-full"
                    onPress={handleGeneratePress}
                    disabled={!selectedProfileId || recommendationsLoading}
                    isLoading={recommendationsLoading}
                  />
                )}
              </View>
            )}
          </View>
        </ScrollView>
      </View>
      <View>
        <AddGeneralNoteModal
          isVisible={isNoteModalVisible}
          onClose={() => setIsNoteModalVisible(false)}
          onSave={handleSaveNote}
        />
        <AddCustomDateModal
          isVisible={isDateModalVisible}
          onClose={() => setIsDateModalVisible(false)}
          onAddItem={handleSaveDate}
          profileId={selectedProfileId}
        />
        <AddPastGiftModal
          isVisible={isGiftModalVisible}
          onClose={() => setIsGiftModalVisible(false)}
          onAddItem={handleSaveGift}
        />
      </View>
    </SafeAreaView>
  );
}
