import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  View,
  Text,
  SafeAreaView,
  ActivityIndicator,
  Pressable,
  ScrollView,
  Modal,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Stack } from 'expo-router';
import AddCustomDateModal from '@/components/profile/AddCustomDateModal';
import Button from '@/components/ui/Button';
import { Timestamp } from 'firebase/firestore';
import { CustomDate } from '@/functions/src/types/firestore';
import { updateSignificantOther } from '@/services/profileService';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
import { Calendar } from 'react-native-calendars';
import { useAuth } from '@/contexts/AuthContext';
import { format } from 'date-fns';
import Animated, {
  FadeIn,
  FadeOut,
  SlideInRight,
  SlideOutRight,
} from 'react-native-reanimated';
import * as Haptics from 'expo-haptics';
import { Feather } from '@expo/vector-icons';
import {
  saveRecommendationFeedback,
  getProfileFeedback,
  deleteRecommendationFeedback,
  type FeedbackEntry,
  type GiftRecommendation,
} from '@/services/recommendationService';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../tailwind.config'; // Corrected path

// Resolve Tailwind config to access theme values
const fullConfig = resolveConfig(tailwindConfig);
const themeColors = fullConfig.theme?.colors || {};

// Helper to safely access nested theme colors
const getThemeColor = (path: string, fallback: string): string => {
  const keys = path.split('.');
  let color = themeColors as any;
  for (const key of keys) {
    if (color && typeof color === 'object' && key in color) {
      color = color[key];
    } else {
      return fallback; // Return fallback if path is invalid
    }
  }
  // Handle cases where the resolved value might be an object (e.g., primary: { 500: ... })
  if (typeof color === 'object' && 'DEFAULT' in color) {
    return color.DEFAULT;
  }
  if (typeof color === 'string') {
    return color;
  }
  return fallback; // Return fallback if final value is not a string
};

// Import new hooks and components
import useCalendarData, { CalendarEvent } from '@/hooks/useCalendarData';
import useCalendarRecommendations from '@/hooks/useCalendarRecommendations';
import CalendarEventList from '@/components/calendar/CalendarEventList';
import RecommendationDisplay from '@/components/calendar/RecommendationDisplay';
import CountdownDisplay from '@/components/calendar/CountdownDisplay';

// Interface for day press params (needed for Calendar component prop)
interface DayPressParams {
  dateString: string;
  day: number;
  month: number;
  year: number;
  timestamp: number;
}

const CalendarScreen = () => {
  // Use the custom hook for calendar data
  const {
    profiles,
    processedEvents,
    calendarMarkings,
    closestDate,
    isLoading,
    error,
    selectedProfileId,
    handleProfileSelect: handleProfileSelectHook, // Rename to avoid conflict
    optimizedDataLoad, // Expose for refreshing after adding date
  } = useCalendarData();

  // Local state for selected date and event for ideas
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedEventForIdeas, setSelectedEventForIdeas] =
    useState<CalendarEvent | null>(null);
  const [showProfileSelector, setShowProfileSelector] = useState(false);
  const [isAddDateModalVisible, setIsAddDateModalVisible] = useState(false);
  
  // Feedback functionality state
  const [currentFeedbackMap, setCurrentFeedbackMap] = useState<
    Map<string, FeedbackEntry>
  >(new Map());
  const [feedbackError, setFeedbackError] = useState<string | null>(null);
  const processingFeedbackRef = useRef(new Set<string>());

  // Use the custom hook for recommendations
  const { recommendations, isLoadingRecommendations, recommendationError } =
    useCalendarRecommendations(selectedEventForIdeas, selectedProfileId); // Pass selectedProfileId

  const { user } = useAuth();

  // Handle day press on the calendar
  const handleDayPress = useCallback((day: DayPressParams) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setSelectedDate(day.dateString);
    setSelectedEventForIdeas(null); // Reset selected event when date changes
  }, []);

  // Handle event selection from the list
  const handleEventSelect = useCallback((event: CalendarEvent) => {
    setSelectedEventForIdeas(event);
  }, []);

  // Handle profile selection from the modal
  const handleProfileSelect = useCallback(
    async (profileId: string) => {
      setShowProfileSelector(false);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      // Call the hook's profile selection handler
      await handleProfileSelectHook(profileId);
      // Reset selected date and event when profile changes
      setSelectedDate(null);
      setSelectedEventForIdeas(null);
    },
    [handleProfileSelectHook]
  );

  // Handle adding a new custom date
  const handleAddNewCustomDate = async (newItem: {
    name: string;
    date: Date | null;
    profileId: string | null;
  }) => {
    if (!user || !selectedProfileId) {
      // Use selectedProfileId from hook
      console.error('User not authenticated or no profile selected.');
      // TODO: Show user feedback
      return;
    }

    if (!newItem.date) {
      console.error('Date is required for a custom date.');
      // TODO: Show user feedback
      return;
    }

    const newCustomDate: CustomDate = {
      id: uuidv4(), // Generate a unique ID
      name: newItem.name,
      date: Timestamp.fromDate(newItem.date), // Convert Date to Timestamp
    };

    // Find the currently selected profile to update its custom dates
    const currentProfile = profiles.find(
      (p) => p.profileId === selectedProfileId
    );

    if (!currentProfile) {
      console.error('Selected profile not found.');
      // TODO: Show user feedback
      return;
    }

    // Add the new custom date to the selected profile's customDates array
    const updatedCustomDates = [
      ...(currentProfile.customDates || []),
      newCustomDate,
    ];
    const updatedProfile = {
      ...currentProfile,
      customDates: updatedCustomDates,
    };

    try {
      await updateSignificantOther(user.uid, selectedProfileId, updatedProfile);
      console.log('Custom date added successfully!');
      setIsAddDateModalVisible(false);
      // Refresh data after adding a new date using the hook's function
      optimizedDataLoad();
    } catch (error) {
      console.error('Error adding custom date:', error);
      // Handle error (e.g., show an alert to the user)
      // TODO: Show user feedback
    }
  };

  // Fetch profile feedback
  const fetchProfileFeedback = useCallback(async (profileId: string) => {
    if (!profileId) return;

    setFeedbackError(null);
    try {
      const feedback = await getProfileFeedback(profileId);
      const feedbackMap = new Map<string, FeedbackEntry>();
      feedback.forEach((entry) => {
        if (entry.recommendationId) {
          feedbackMap.set(entry.recommendationId, entry);
        }
      });
      setCurrentFeedbackMap(feedbackMap);
    } catch (error) {
      setFeedbackError('Could not load feedback data.');
      setCurrentFeedbackMap(new Map());
    }
  }, []);

  // Handle feedback (like/dislike)
  const handleFeedback = async (
    item: GiftRecommendation,
    feedbackType: 'like' | 'dislike'
  ) => {
    const recommendationId = item.id || item.recommendationId;

    if (
      !user?.uid ||
      !selectedProfileId ||
      !recommendationId ||
      typeof recommendationId !== 'string'
    ) {
      return;
    }
    
    if (
      recommendationId &&
      processingFeedbackRef.current.has(recommendationId)
    ) {
      return;
    }

    if (recommendationId) {
      processingFeedbackRef.current.add(recommendationId);
    }
    
    const existingFeedback = recommendationId
      ? currentFeedbackMap.get(recommendationId)
      : undefined;
    const newFeedbackMap = new Map(currentFeedbackMap);

    try {
      if (existingFeedback?.feedbackType === feedbackType) {
        // Same feedback type clicked - remove it (toggle off)
        if (recommendationId) {
          newFeedbackMap.delete(recommendationId);
        }
        setCurrentFeedbackMap(newFeedbackMap);
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        if (existingFeedback.id) {
          await deleteRecommendationFeedback(existingFeedback.id);
        }
      } else {
        // Different feedback type clicked - replace the existing one
        if (recommendationId) {
          // First delete the existing feedback if it exists
          if (existingFeedback?.id) {
            await deleteRecommendationFeedback(existingFeedback.id);
          }

          const tempFeedback: FeedbackEntry = {
            id: uuidv4(),
            userId: user.uid,
            profileId: selectedProfileId,
            recommendationId,
            feedbackType,
            recommendationDetails: {
              name: item.name,
              description: item.description,
            },
            timestamp: new Date(),
          };
          newFeedbackMap.set(recommendationId, tempFeedback);
          setCurrentFeedbackMap(newFeedbackMap);

          Haptics.impactAsync(
            feedbackType === 'like'
              ? Haptics.ImpactFeedbackStyle.Light
              : Haptics.ImpactFeedbackStyle.Medium
          );

          const success = await saveRecommendationFeedback({
            userId: user.uid,
            profileId: selectedProfileId,
            recommendationId,
            feedbackType,
            recommendationDetails: {
              name: item.name,
              description: item.description,
            },
          });
          if (!success) throw new Error('Save feedback failed');

          await fetchProfileFeedback(selectedProfileId);
        }
      }
    } catch (err) {
      // Revert optimistic update
      if (existingFeedback && recommendationId) {
        newFeedbackMap.set(recommendationId, existingFeedback);
      } else if (recommendationId) {
        newFeedbackMap.delete(recommendationId);
      }
      setCurrentFeedbackMap(newFeedbackMap);
      Alert.alert('Error', 'Could not save your feedback. Please try again.');
    } finally {
      if (recommendationId) {
        processingFeedbackRef.current.delete(recommendationId);
      }
    }
  };

  // Load feedback when profile changes
  useEffect(() => {
    if (selectedProfileId) {
      fetchProfileFeedback(selectedProfileId);
    }
  }, [selectedProfileId, fetchProfileFeedback]);

  // Get the events for the selected date from the processedEvents map
  const selectedDateEvents = selectedDate
    ? processedEvents[selectedDate] || []
    : [];

  // Find the selected profile for displaying its name in the selector
  const selectedProfile = profiles.find(
    (p) => p.profileId === selectedProfileId
  );

  return (
    <>
      <SafeAreaView className="flex-1 bg-background dark:bg-background-dark">
        <Stack.Screen
          options={{
            title: 'Calendar',
            headerShown: false, // Hide default header
            headerLargeTitle: true,
            headerTitleStyle: {
              color: '#E87900',
            },
            headerTransparent: true,
            headerBlurEffect: 'extraLight',
            headerStyle: {},
            // Removed headerRight here
          }}
        />

        {isLoading ? (
          <View className="flex-1 justify-center items-center">
            <ActivityIndicator
              size="large"
              color={getThemeColor('primary.500', '#E87900')}
            />
          </View>
        ) : error ? (
          <View className="items-center p-6 mt-8 rounded-xl shadow-sm bg-card dark:bg-card-dark">
            <Text className="text-base font-medium text-error">{error}</Text>
          </View>
        ) : (
          <ScrollView
            contentContainerStyle={{ paddingTop: 20 }} // Adjusted padding
            className="flex-1"
            showsVerticalScrollIndicator={false}
          >
            {/* Custom Header */}
            <View className="flex-row justify-between items-center px-4 mb-8">
              {/* Title */}
              <Text className="text-2xl font-bold text-primary-500 dark:text-primary-dark">
                Calendar
              </Text>

              {/* Profile Selector and Add Date Button */}
              <View className="flex-row items-center w-1/2">
                {/* Profile Selector */}
                {profiles.length > 0 && (
                  <Animated.View
                    entering={FadeIn.duration(300)}
                    exiting={FadeOut.duration(200)}
                    className="w-full"
                  >
                    <Pressable
                      onPress={() => {
                        setShowProfileSelector(true);
                        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      }}
                      className="flex-row justify-between items-center p-2 rounded-xl border shadow-sm border-border dark:border-border-dark bg-card dark:bg-card-dark"
                    >
                      <Text>Profile:</Text>
                      <Text className="text-base font-medium text-primary-500">
                        {selectedProfile?.name || 'Select Profile'}
                      </Text>
                      <Feather
                        name="chevron-down"
                        size={20}
                        color={getThemeColor('text-secondary', '#4B5563')}
                      />
                    </Pressable>
                  </Animated.View>
                )}
              </View>
              {/* Add Date Button */}
              <TouchableOpacity
                onPress={() => {
                  setIsAddDateModalVisible(true);
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                }}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }} // Increase tap area
                className="flex flex-row gap-2 items-center"
              >
                <Text className="text-primary-500">Add Date</Text>
                <Feather
                  name="plus-circle"
                  size={24}
                  color={getThemeColor('primary.500', '#E87900')}
                />
              </TouchableOpacity>
            </View>

            {/* Rest of the content */}
            <View className="px-4">
              {/* Feedback Error */}
              {feedbackError && (
                <Animated.View
                  entering={FadeIn.duration(300)}
                  className="p-3 mb-4 rounded-md bg-error/20 dark:bg-error-dark/20"
                >
                  <Text className="text-sm text-center text-error dark:text-error-dark">
                    {feedbackError}
                  </Text>
                </Animated.View>
              )}

              {/* Closest Upcoming Date */}
              <CountdownDisplay closestDate={closestDate} />

              {/* Calendar Legend */}
              <View className="flex-row justify-around px-2 mt-2 mb-5">
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-birthday"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Birthday
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-anniversary"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Anniversary
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-customDate"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Custom
                  </Text>
                </View>
                <View className="flex-row items-center">
                  <View className="mr-2 w-3 h-3 rounded-full bg-holiday"></View>
                  <Text className="text-xs font-medium text-text-secondary dark:text-text-secondary-dark">
                    Holiday
                  </Text>
                </View>
              </View>

              {/* Calendar */}
              <Animated.View
                entering={FadeIn.duration(400)}
                exiting={FadeOut.duration(300)}
                className="overflow-hidden mb-4 rounded-xl shadow-sm bg-card dark:bg-card-dark"
              >
                <Calendar
                  markingType={'multi-dot'}
                  markedDates={{
                    ...calendarMarkings, // Use markings from the hook
                    ...(selectedDate && {
                      [selectedDate]: {
                        ...calendarMarkings[selectedDate],
                        selected: true,
                        selectedColor: '#A3002B',
                      },
                    }),
                  }}
                  onDayPress={handleDayPress} // Use the local handler
                  enableSwipeMonths={true}
                  theme={{
                    backgroundColor: getThemeColor('card', '#FFFFFF'),
                    calendarBackground: getThemeColor('card', '#FFFFFF'),
                    textSectionTitleColor: getThemeColor(
                      'text-secondary',
                      '#4B5563'
                    ),
                    selectedDayBackgroundColor: getThemeColor(
                      'accent.500',
                      '#A3002B'
                    ),
                    selectedDayTextColor: getThemeColor('card', '#FFFFFF'), // Using card color for white
                    todayTextColor: getThemeColor('primary.500', '#E87900'),
                    dayTextColor: getThemeColor('text-primary', '#1F2937'),
                    textDisabledColor: getThemeColor('disabled', '#9CA3AF'),
                    dotColor: getThemeColor('primary.500', '#E87900'), // Default dot color
                    selectedDotColor: getThemeColor('card', '#FFFFFF'), // Using card color for white
                    arrowColor: getThemeColor('primary.500', '#E87900'),
                    disabledArrowColor: getThemeColor('disabled', '#9CA3AF'),
                    monthTextColor: getThemeColor('text-primary', '#1F2937'),
                    indicatorColor: getThemeColor('primary.500', '#E87900'),
                    textDayFontWeight: '500',
                    textMonthFontWeight: '600',
                    textDayHeaderFontWeight: '500',
                    textDayFontSize: 16,
                    textMonthFontSize: 16,
                    textDayHeaderFontSize: 14,
                  }}
                />
              </Animated.View>

              {/* Events for Selected Date */}
              {selectedDate && (
                <Animated.View
                  entering={SlideInRight.duration(300)}
                  exiting={SlideOutRight.duration(200)}
                >
                  <CalendarEventList
                    events={selectedDateEvents} // Pass events for the selected date
                    onEventSelect={handleEventSelect} // Pass the event selection handler
                  />
                </Animated.View>
              )}

              {/* Gift Recommendations */}
              <RecommendationDisplay
                recommendations={recommendations}
                isLoading={isLoadingRecommendations}
                error={recommendationError}
                currentFeedbackMap={currentFeedbackMap}
                onFeedback={handleFeedback}
                processingFeedbackRef={processingFeedbackRef}
                feedbackError={feedbackError}
              />
            </View>
          </ScrollView>
        )}
      </SafeAreaView>

      {/* Profile Selector Modal */}
      <Modal
        visible={showProfileSelector}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowProfileSelector(false)}
      >
        <Pressable
          className="flex-1 justify-center items-center p-4 bg-black/60"
          onPress={() => setShowProfileSelector(false)}
        >
          <Animated.View
            entering={FadeIn.duration(300)}
            exiting={FadeOut.duration(200)}
            className="p-5 w-full max-w-sm rounded-2xl shadow-lg bg-card dark:bg-card-dark"
          >
            <Text className="mb-5 text-lg font-bold text-text-primary dark:text-text-primary-dark">
              Select Profile
            </Text>
            {profiles.map((profile, index) => (
              <Pressable
                key={profile.profileId}
                className={`py-4 ${
                  index === profiles.length - 1
                    ? ''
                    : 'border-b border-border dark:border-border-dark'
                }`}
                onPress={() => handleProfileSelect(profile.profileId)} // Use the local handler
              >
                <Text
                  className={`text-base ${
                    profile.profileId === selectedProfileId
                      ? 'font-bold text-primary-500' // Already using theme color
                      : 'text-text-primary dark:text-text-primary-dark' // Already using theme color
                  }`}
                >
                  {profile.name}
                </Text>
              </Pressable>
            ))}
            <SafeAreaView>
              <View className="pt-4">
                <Pressable
                  onPress={() => setShowProfileSelector(false)}
                  className="items-center px-4 py-3 mt-2 rounded-xl bg-background dark:bg-background-dark"
                >
                  <Text className="text-base font-semibold text-text-primary dark:text-text-primary-dark">
                    Cancel
                  </Text>
                </Pressable>
              </View>
            </SafeAreaView>
          </Animated.View>
        </Pressable>
      </Modal>

      {/* Add Custom Date Modal */}
      <AddCustomDateModal
        isVisible={isAddDateModalVisible}
        onClose={() => setIsAddDateModalVisible(false)}
        onAddItem={handleAddNewCustomDate} // Corrected prop name
        profileId={selectedProfileId} // Pass selected profile ID from hook
      />
    </>
  );
};

export default CalendarScreen;
